# PowerShell script to find differences between two API endpoint files

Write-Host "Reading files..."

# Read both files
$file1 = Get-Content "api_endpoints_with_userid.txt"
$file2 = Get-Content "BNI_API_Endpoints.txt"

Write-Host "File 1 (api_endpoints_with_userid.txt): $($file1.Count) lines"
Write-Host "File 2 (BNI_API_Endpoints.txt): $($file2.Count) lines"

# Convert to hash sets for faster comparison
$set1 = [System.Collections.Generic.HashSet[string]]::new()
$set2 = [System.Collections.Generic.HashSet[string]]::new()

# Add lines to sets (normalize by trimming whitespace)
foreach ($line in $file1) {
    if ($line.Trim() -ne "") {
        $set1.Add($line.Trim()) | Out-Null
    }
}

foreach ($line in $file2) {
    if ($line.Trim() -ne "") {
        $set2.Add($line.Trim()) | Out-Null
    }
}

Write-Host "Unique lines in file 1: $($set1.Count)"
Write-Host "Unique lines in file 2: $($set2.Count)"

# Find differences
$onlyInFile1 = [System.Collections.Generic.List[string]]::new()
$onlyInFile2 = [System.Collections.Generic.List[string]]::new()

# Lines only in file 1
foreach ($line in $set1) {
    if (-not $set2.Contains($line)) {
        $onlyInFile1.Add($line)
    }
}

# Lines only in file 2
foreach ($line in $set2) {
    if (-not $set1.Contains($line)) {
        $onlyInFile2.Add($line)
    }
}

Write-Host "Lines only in api_endpoints_with_userid.txt: $($onlyInFile1.Count)"
Write-Host "Lines only in BNI_API_Endpoints.txt: $($onlyInFile2.Count)"

# Create output file with all differences
$outputFile = "differences_between_files.txt"
$output = @()

if ($onlyInFile1.Count -gt 0) {
    $output += "=== LINES ONLY IN api_endpoints_with_userid.txt ==="
    $output += $onlyInFile1 | Sort-Object
    $output += ""
}

if ($onlyInFile2.Count -gt 0) {
    $output += "=== LINES ONLY IN BNI_API_Endpoints.txt ==="
    $output += $onlyInFile2 | Sort-Object
}

$output | Out-File -FilePath $outputFile -Encoding UTF8

Write-Host "Differences saved to: $outputFile"
Write-Host "Total differences: $($onlyInFile1.Count + $onlyInFile2.Count)"
