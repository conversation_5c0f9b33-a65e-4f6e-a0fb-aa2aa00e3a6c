#!/usr/bin/env python3
"""
Bearer Token Test Script
Tests the bearer token functionality and API authentication.
"""

import requests
import json
import logging
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_bearer_token():
    """Load bearer token from file."""
    try:
        with open('bearer_token.txt', 'r') as f:
            token = f.read().strip()
        if token:
            logger.info("✅ Bearer token loaded from file")
            return token
    except FileNotFoundError:
        logger.warning("⚠️ No saved bearer token found")
    return None

def test_api_with_token(token, test_endpoints=None):
    """
    Test API endpoints with the provided bearer token.
    
    Args:
        token: Bearer token to test
        test_endpoints: List of endpoints to test (optional)
    """
    if not token:
        logger.error("❌ No bearer token provided")
        return False

    # Default test endpoints
    if test_endpoints is None:
        test_endpoints = [
            "https://www.bniconnectglobal.com/api/v1/user/profile",
            "https://www.bniconnectglobal.com/api/v1/dashboard/stats",
            "https://www.bniconnectglobal.com/api/v1/members/search?limit=1"
        ]

    # Set up session
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Origin': 'https://www.bniconnectglobal.com',
        'Referer': 'https://www.bniconnectglobal.com/',
        'Authorization': f'Bearer {token}'
    })

    logger.info(f"🔍 Testing bearer token with {len(test_endpoints)} endpoints...")
    
    success_count = 0
    total_tests = len(test_endpoints)
    
    for i, endpoint in enumerate(test_endpoints, 1):
        logger.info(f"🧪 Test {i}/{total_tests}: {endpoint}")
        
        try:
            response = session.get(endpoint, timeout=15)
            
            if response.status_code == 200:
                logger.info(f"✅ SUCCESS: Status 200 - Token is valid")
                success_count += 1
                
                # Try to parse JSON response
                try:
                    data = response.json()
                    logger.info(f"📄 Response preview: {str(data)[:100]}...")
                except json.JSONDecodeError:
                    logger.info(f"📄 Response is not JSON (length: {len(response.text)})")
                    
            elif response.status_code in [401, 403]:
                logger.error(f"❌ AUTH FAILED: Status {response.status_code} - Token is invalid or expired")
                
            elif response.status_code == 404:
                logger.warning(f"⚠️ NOT FOUND: Status 404 - Endpoint may not exist")
                
            else:
                logger.warning(f"⚠️ UNEXPECTED: Status {response.status_code}")
                
        except requests.exceptions.Timeout:
            logger.error(f"⏰ TIMEOUT: Request took too long")
            
        except requests.exceptions.RequestException as e:
            logger.error(f"🌐 REQUEST ERROR: {e}")
        
        except Exception as e:
            logger.error(f"❌ UNEXPECTED ERROR: {e}")
    
    # Summary
    logger.info(f"\n📊 TEST SUMMARY:")
    logger.info(f"  Total tests: {total_tests}")
    logger.info(f"  Successful: {success_count}")
    logger.info(f"  Failed: {total_tests - success_count}")
    logger.info(f"  Success rate: {success_count/total_tests*100:.1f}%")
    
    if success_count > 0:
        logger.info(f"✅ Bearer token appears to be working!")
        return True
    else:
        logger.error(f"❌ Bearer token is not working - may need refresh")
        return False

def test_sample_member_endpoint():
    """Test with a sample member endpoint if available."""
    
    # Check if we have sample endpoints
    endpoints_file = 'api_endpoints_with_userid.txt'
    if not os.path.exists(endpoints_file):
        logger.warning(f"⚠️ No endpoints file found: {endpoints_file}")
        return False
    
    # Load a few sample endpoints
    try:
        with open(endpoints_file, 'r') as f:
            lines = [line.strip() for line in f if line.strip()]
        
        if not lines:
            logger.warning(f"⚠️ Endpoints file is empty")
            return False
        
        # Test first 3 endpoints
        sample_endpoints = []
        for line in lines[:3]:
            try:
                parts = line.split()
                if len(parts) >= 2:
                    api_url = parts[0]
                    sample_endpoints.append(api_url)
            except:
                continue
        
        if sample_endpoints:
            logger.info(f"🧪 Testing with {len(sample_endpoints)} sample member endpoints...")
            token = load_bearer_token()
            return test_api_with_token(token, sample_endpoints)
        else:
            logger.warning(f"⚠️ Could not parse any endpoints from file")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing sample endpoints: {e}")
        return False

def main():
    """Main function to test bearer token."""
    print("🔐 BNI API Bearer Token Test")
    print("=" * 40)
    
    # Load token
    token = load_bearer_token()
    
    if not token:
        print("❌ No bearer token found!")
        print("Please run the main scraper first to generate a token.")
        return
    
    print(f"🔑 Token preview: {token[:20]}...{token[-10:]}")
    print()
    
    # Test with default endpoints
    print("🧪 Testing with default API endpoints...")
    default_success = test_api_with_token(token)
    
    print("\n" + "="*40)
    
    # Test with sample member endpoints
    print("🧪 Testing with sample member endpoints...")
    sample_success = test_sample_member_endpoint()
    
    print("\n" + "="*40)
    print("🎯 FINAL RESULT:")
    
    if default_success or sample_success:
        print("✅ Bearer token is working correctly!")
        print("The API scraper should work without auth errors.")
    else:
        print("❌ Bearer token is not working!")
        print("You may need to:")
        print("1. Run the main scraper to refresh the token")
        print("2. Check your BNI credentials in .env file")
        print("3. Verify your internet connection")

if __name__ == "__main__":
    main()
