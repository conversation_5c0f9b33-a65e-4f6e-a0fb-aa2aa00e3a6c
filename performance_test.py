#!/usr/bin/env python3
"""
Performance test script to compare original vs optimized API scraper.
This script creates sample endpoints and measures processing time.
"""

import time
import requests
import json
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_endpoints(count=100):
    """Create sample endpoint file for testing."""
    sample_endpoints = []
    
    for i in range(count):
        # Create sample API URLs with UUIDs
        uuid = f"test-uuid-{i:04d}"
        user_id = 1000 + i
        api_url = f"https://httpbin.org/delay/0.1?uuId={uuid}"  # Simulates 100ms API response
        
        sample_endpoints.append(f"{api_url} user_id={user_id}")
    
    # Write to file
    with open('sample_endpoints.txt', 'w') as f:
        f.write('\n'.join(sample_endpoints))
    
    logger.info(f"Created {count} sample endpoints in 'sample_endpoints.txt'")
    return 'sample_endpoints.txt'

def test_sequential_processing(endpoints_file, delay=0.5):
    """Test sequential processing (original method)."""
    logger.info(f"🐌 Testing SEQUENTIAL processing with {delay}s delay...")
    
    start_time = time.time()
    processed = 0
    
    with open(endpoints_file, 'r') as f:
        endpoints = [line.strip() for line in f if line.strip()]
    
    for endpoint_line in endpoints:
        try:
            # Parse endpoint
            parts = endpoint_line.split()
            api_url = parts[0]
            
            # Make request
            response = requests.get(api_url, timeout=10)
            
            if response.status_code == 200:
                processed += 1
            
            # Add delay (original bottleneck)
            time.sleep(delay)
            
        except Exception as e:
            logger.error(f"Error processing {endpoint_line}: {e}")
    
    total_time = time.time() - start_time
    
    logger.info(f"Sequential Results:")
    logger.info(f"  Processed: {processed}/{len(endpoints)}")
    logger.info(f"  Total time: {total_time:.2f} seconds")
    logger.info(f"  Time per request: {total_time/len(endpoints):.3f} seconds")
    
    return total_time, processed

def test_concurrent_processing(endpoints_file, max_workers=10):
    """Test concurrent processing (optimized method)."""
    import concurrent.futures
    
    logger.info(f"🚀 Testing CONCURRENT processing with {max_workers} workers...")
    
    start_time = time.time()
    processed = 0
    
    with open(endpoints_file, 'r') as f:
        endpoints = [line.strip() for line in f if line.strip()]
    
    def fetch_single(endpoint_line):
        try:
            parts = endpoint_line.split()
            api_url = parts[0]
            
            response = requests.get(api_url, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    # Process concurrently
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(fetch_single, endpoints))
        processed = sum(results)
    
    total_time = time.time() - start_time
    
    logger.info(f"Concurrent Results:")
    logger.info(f"  Processed: {processed}/{len(endpoints)}")
    logger.info(f"  Total time: {total_time:.2f} seconds")
    logger.info(f"  Time per request: {total_time/len(endpoints):.3f} seconds")
    
    return total_time, processed

def main():
    """Run performance comparison test."""
    print("🧪 BNI API Scraper Performance Test")
    print("=" * 50)
    
    # Create sample endpoints
    sample_count = 50  # Smaller number for quick test
    endpoints_file = create_sample_endpoints(sample_count)
    
    print(f"\nTesting with {sample_count} sample endpoints...")
    print("Note: Using httpbin.org/delay/0.1 to simulate 100ms API responses")
    
    # Test sequential processing (original method)
    print("\n" + "="*50)
    sequential_time, seq_processed = test_sequential_processing(endpoints_file, delay=0.5)
    
    # Test concurrent processing (optimized method)
    print("\n" + "="*50)
    concurrent_time, conc_processed = test_concurrent_processing(endpoints_file, max_workers=10)
    
    # Calculate improvement
    print("\n" + "="*50)
    print("📊 PERFORMANCE COMPARISON")
    print("="*50)
    
    if sequential_time > 0:
        speedup = sequential_time / concurrent_time
        time_saved = sequential_time - concurrent_time
        
        print(f"Sequential (Original):  {sequential_time:.2f}s ({sequential_time/sample_count:.3f}s per request)")
        print(f"Concurrent (Optimized): {concurrent_time:.2f}s ({concurrent_time/sample_count:.3f}s per request)")
        print(f"")
        print(f"🚀 Speed improvement: {speedup:.1f}x faster")
        print(f"⏰ Time saved: {time_saved:.2f} seconds ({time_saved/60:.1f} minutes)")
        print(f"")
        
        # Extrapolate to larger datasets
        for dataset_size in [1000, 5000, 10000]:
            original_time = dataset_size * (sequential_time / sample_count)
            optimized_time = dataset_size * (concurrent_time / sample_count)
            time_saved_large = original_time - optimized_time
            
            print(f"For {dataset_size:,} endpoints:")
            print(f"  Original: {original_time/3600:.1f} hours")
            print(f"  Optimized: {optimized_time/60:.1f} minutes")
            print(f"  Time saved: {time_saved_large/3600:.1f} hours")
            print()
    
    print("🎯 RECOMMENDATIONS:")
    print("1. Use FAST MODE (optimized) for large datasets")
    print("2. Reduce delay_between_requests from 2.0s to 0.1s or less")
    print("3. Use concurrent processing with 5-15 workers")
    print("4. Process in batches for better memory management")
    
    # Cleanup
    import os
    try:
        os.remove(endpoints_file)
        logger.info("Cleaned up sample endpoints file")
    except:
        pass

if __name__ == "__main__":
    main()
