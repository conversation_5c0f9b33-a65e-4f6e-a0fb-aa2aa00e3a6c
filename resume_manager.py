#!/usr/bin/env python3
"""
Resume Manager for BNI API Scraper
Utility to manage resume functionality and analyze progress.
"""

import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class ResumeManager:
    def __init__(self, resume_file: str = "processed_uuids.txt", endpoints_file: str = "api_endpoints_with_userid.txt"):
        self.resume_file = resume_file
        self.endpoints_file = endpoints_file

    def load_processed_uuids(self) -> set:
        """Load processed UUIDs from resume file."""
        processed_uuids = set()
        
        try:
            if os.path.exists(self.resume_file):
                with open(self.resume_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        uuid = line.strip()
                        if uuid:
                            processed_uuids.add(uuid)
        except Exception as e:
            logger.error(f"Error loading processed UUIDs: {e}")
        
        return processed_uuids

    def load_all_endpoints(self) -> list:
        """Load all endpoints from the endpoints file."""
        endpoints = []
        
        try:
            if os.path.exists(self.endpoints_file):
                with open(self.endpoints_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            # Extract UUID from the line
                            try:
                                parts = line.split()
                                if len(parts) >= 2:
                                    api_url = parts[0]
                                    uuid = api_url.split('uuId=')[-1].strip()
                                    endpoints.append((line, uuid))
                            except Exception as e:
                                logger.warning(f"Could not parse line: {line}")
        except Exception as e:
            logger.error(f"Error loading endpoints: {e}")
        
        return endpoints

    def get_progress_stats(self) -> dict:
        """Get detailed progress statistics."""
        processed_uuids = self.load_processed_uuids()
        all_endpoints = self.load_all_endpoints()
        
        total_endpoints = len(all_endpoints)
        processed_count = len(processed_uuids)
        remaining_count = total_endpoints - processed_count
        progress_percentage = (processed_count / total_endpoints * 100) if total_endpoints > 0 else 0
        
        # Find remaining endpoints
        all_uuids = {uuid for _, uuid in all_endpoints}
        remaining_uuids = all_uuids - processed_uuids
        
        return {
            'total_endpoints': total_endpoints,
            'processed_count': processed_count,
            'remaining_count': remaining_count,
            'progress_percentage': progress_percentage,
            'processed_uuids': processed_uuids,
            'remaining_uuids': remaining_uuids,
            'all_uuids': all_uuids
        }

    def show_progress(self):
        """Display current progress."""
        stats = self.get_progress_stats()
        
        print("\n📊 BNI API Scraper - Progress Report")
        print("=" * 50)
        print(f"📁 Resume file: {self.resume_file}")
        print(f"📁 Endpoints file: {self.endpoints_file}")
        print(f"📅 Report time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        print(f"📈 PROGRESS STATISTICS:")
        print(f"  Total endpoints: {stats['total_endpoints']:,}")
        print(f"  Processed: {stats['processed_count']:,}")
        print(f"  Remaining: {stats['remaining_count']:,}")
        print(f"  Progress: {stats['progress_percentage']:.1f}%")
        print()
        
        if stats['processed_count'] > 0:
            print(f"✅ Resume file exists with {stats['processed_count']:,} processed UUIDs")
            if stats['remaining_count'] > 0:
                print(f"🔄 Script will resume and process {stats['remaining_count']:,} remaining endpoints")
            else:
                print(f"🎉 All endpoints have been processed!")
        else:
            print(f"🆕 No resume file found - script will start from the beginning")

    def clear_resume(self):
        """Clear the resume file."""
        try:
            if os.path.exists(self.resume_file):
                os.remove(self.resume_file)
                print(f"🗑️ Cleared resume file: {self.resume_file}")
            else:
                print(f"📂 No resume file found to clear")
        except Exception as e:
            print(f"❌ Error clearing resume file: {e}")

    def backup_resume(self):
        """Create a backup of the resume file."""
        try:
            if os.path.exists(self.resume_file):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_file = f"{self.resume_file}.backup_{timestamp}"
                
                with open(self.resume_file, 'r') as src, open(backup_file, 'w') as dst:
                    dst.write(src.read())
                
                print(f"💾 Created backup: {backup_file}")
            else:
                print(f"📂 No resume file found to backup")
        except Exception as e:
            print(f"❌ Error creating backup: {e}")

    def create_remaining_endpoints_file(self):
        """Create a file with only the remaining endpoints."""
        try:
            stats = self.get_progress_stats()
            
            if stats['remaining_count'] == 0:
                print(f"🎉 No remaining endpoints to process!")
                return
            
            all_endpoints = self.load_all_endpoints()
            remaining_file = f"remaining_endpoints_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            with open(remaining_file, 'w', encoding='utf-8') as f:
                for line, uuid in all_endpoints:
                    if uuid in stats['remaining_uuids']:
                        f.write(f"{line}\n")
            
            print(f"📝 Created file with {stats['remaining_count']:,} remaining endpoints: {remaining_file}")
            
        except Exception as e:
            print(f"❌ Error creating remaining endpoints file: {e}")

    def validate_resume_file(self):
        """Validate the resume file for any issues."""
        try:
            processed_uuids = self.load_processed_uuids()
            all_endpoints = self.load_all_endpoints()
            all_uuids = {uuid for _, uuid in all_endpoints}
            
            print("\n🔍 RESUME FILE VALIDATION:")
            print("=" * 40)
            
            # Check for duplicates in resume file
            uuid_list = []
            if os.path.exists(self.resume_file):
                with open(self.resume_file, 'r') as f:
                    uuid_list = [line.strip() for line in f if line.strip()]
            
            duplicates = len(uuid_list) - len(set(uuid_list))
            if duplicates > 0:
                print(f"⚠️ Found {duplicates} duplicate UUIDs in resume file")
            else:
                print(f"✅ No duplicates found in resume file")
            
            # Check for UUIDs in resume file that don't exist in endpoints file
            orphaned_uuids = processed_uuids - all_uuids
            if orphaned_uuids:
                print(f"⚠️ Found {len(orphaned_uuids)} UUIDs in resume file that don't exist in endpoints file")
                print(f"   First 5 orphaned UUIDs: {list(orphaned_uuids)[:5]}")
            else:
                print(f"✅ All UUIDs in resume file exist in endpoints file")
            
            # Check file sizes
            resume_size = os.path.getsize(self.resume_file) if os.path.exists(self.resume_file) else 0
            endpoints_size = os.path.getsize(self.endpoints_file) if os.path.exists(self.endpoints_file) else 0
            
            print(f"📊 File sizes:")
            print(f"   Resume file: {resume_size:,} bytes")
            print(f"   Endpoints file: {endpoints_size:,} bytes")
            
        except Exception as e:
            print(f"❌ Error validating resume file: {e}")

def main():
    """Main function for resume manager."""
    manager = ResumeManager()
    
    while True:
        print("\n🔧 BNI API Scraper - Resume Manager")
        print("=" * 40)
        print("1. Show progress report")
        print("2. Clear resume file (start fresh)")
        print("3. Backup resume file")
        print("4. Create remaining endpoints file")
        print("5. Validate resume file")
        print("6. Exit")
        
        choice = input("\nSelect option (1-6): ").strip()
        
        if choice == '1':
            manager.show_progress()
        elif choice == '2':
            confirm = input("⚠️ This will clear all progress. Are you sure? (y/N): ").strip().lower()
            if confirm == 'y':
                manager.clear_resume()
            else:
                print("❌ Cancelled")
        elif choice == '3':
            manager.backup_resume()
        elif choice == '4':
            manager.create_remaining_endpoints_file()
        elif choice == '5':
            manager.validate_resume_file()
        elif choice == '6':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid option. Please enter 1-6.")

if __name__ == "__main__":
    main()
