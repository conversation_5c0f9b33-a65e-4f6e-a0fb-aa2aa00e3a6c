#!/usr/bin/env python3
"""
Database schema checker for member_details table.
This script checks the table structure and identifies primary keys/unique constraints.
"""

import mysql.connector
from mysql.connector import Error
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

def check_table_schema():
    """Check the member_details table schema and constraints."""
    
    # Database configuration - UPDATE THESE VALUES
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'database': 'bni',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4',
        'use_unicode': True
    }
    
    try:
        # Connect to database
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        print("🔍 Checking member_details table schema...")
        print("=" * 60)
        
        # Check if table exists
        cursor.execute("SHOW TABLES LIKE 'member_details'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ Table 'member_details' does not exist!")
            print("\n📝 Suggested CREATE TABLE statement:")
            print(get_suggested_schema())
            return
        
        print("✅ Table 'member_details' exists")
        
        # Get table structure
        print("\n📋 Table Structure:")
        cursor.execute("DESCRIBE member_details")
        columns = cursor.fetchall()
        
        for column in columns:
            field, type_, null, key, default, extra = column
            key_info = f" [{key}]" if key else ""
            null_info = "NULL" if null == "YES" else "NOT NULL"
            print(f"  {field:<25} {type_:<20} {null_info:<8} {key_info}")
        
        # Check primary key
        print("\n🔑 Primary Key Information:")
        cursor.execute("""
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = %s 
            AND TABLE_NAME = 'member_details' 
            AND CONSTRAINT_NAME = 'PRIMARY'
        """, (db_config['database'],))
        
        primary_keys = cursor.fetchall()
        if primary_keys:
            pk_columns = [pk[0] for pk in primary_keys]
            print(f"  Primary Key: {', '.join(pk_columns)}")
        else:
            print("  ⚠️ No primary key found!")
        
        # Check unique constraints
        print("\n🔒 Unique Constraints:")
        cursor.execute("""
            SELECT CONSTRAINT_NAME, COLUMN_NAME 
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = %s 
            AND TABLE_NAME = 'member_details' 
            AND CONSTRAINT_NAME != 'PRIMARY'
        """, (db_config['database'],))
        
        unique_constraints = cursor.fetchall()
        if unique_constraints:
            for constraint_name, column_name in unique_constraints:
                print(f"  {constraint_name}: {column_name}")
        else:
            print("  No unique constraints found")
        
        # Check indexes
        print("\n📊 Indexes:")
        cursor.execute("SHOW INDEX FROM member_details")
        indexes = cursor.fetchall()
        
        index_info = {}
        for index in indexes:
            key_name = index[2]
            column_name = index[4]
            if key_name not in index_info:
                index_info[key_name] = []
            index_info[key_name].append(column_name)
        
        for index_name, columns in index_info.items():
            print(f"  {index_name}: {', '.join(columns)}")
        
        # Analyze duplicate prevention strategy
        print("\n🛡️ Duplicate Prevention Analysis:")
        if primary_keys:
            pk_columns = [pk[0] for pk in primary_keys]
            print(f"  ✅ ON DUPLICATE KEY UPDATE will work with: {', '.join(pk_columns)}")
            
            # Check if user_id or uuid are in primary key
            if 'user_id' in pk_columns:
                print("  ✅ user_id is part of primary key - duplicates prevented by user_id")
            elif 'uuid' in pk_columns:
                print("  ✅ uuid is part of primary key - duplicates prevented by uuid")
            else:
                print("  ⚠️ Neither user_id nor uuid are in primary key")
                print("     Consider adding UNIQUE constraint on user_id or uuid")
        else:
            print("  ❌ No primary key - ON DUPLICATE KEY UPDATE won't work properly!")
            print("     You need to add a primary key or unique constraint")
        
        # Sample data check
        print("\n📊 Sample Data (first 3 records):")
        cursor.execute("SELECT user_id, uuid, display_name, company_name FROM member_details LIMIT 3")
        sample_data = cursor.fetchall()
        
        if sample_data:
            print("  user_id | uuid | display_name | company_name")
            print("  " + "-" * 50)
            for row in sample_data:
                user_id, uuid, display_name, company_name = row
                print(f"  {user_id or 'NULL':<7} | {(uuid or 'NULL')[:8]:<8} | {(display_name or 'NULL')[:12]:<12} | {(company_name or 'NULL')[:15]}")
        else:
            print("  No data found in table")
        
        # Count total records
        cursor.execute("SELECT COUNT(*) FROM member_details")
        total_count = cursor.fetchone()[0]
        print(f"\n📈 Total records in table: {total_count:,}")
        
        # Check for potential duplicates
        if 'user_id' in [pk[0] for pk in primary_keys] or any('user_id' in str(uc) for uc in unique_constraints):
            cursor.execute("SELECT COUNT(*), COUNT(DISTINCT user_id) FROM member_details WHERE user_id IS NOT NULL")
            total, unique_user_ids = cursor.fetchone()
            if total != unique_user_ids:
                print(f"  ⚠️ Potential duplicates: {total} total vs {unique_user_ids} unique user_ids")
            else:
                print(f"  ✅ No duplicates found based on user_id")
        
    except Error as e:
        logger.error(f"Database error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            logger.info("Database connection closed")

def get_suggested_schema():
    """Return suggested CREATE TABLE statement."""
    return """
CREATE TABLE member_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNIQUE NOT NULL,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    extraction_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    title VARCHAR(100),
    display_name VARCHAR(255),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    email_address VARCHAR(255),
    phone_number VARCHAR(50),
    mobile_number VARCHAR(50),
    direct_number VARCHAR(50),
    fax_number VARCHAR(50),
    website_url TEXT,
    profile_image_id VARCHAR(255),
    address_line1 TEXT,
    address_line2 TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(10),
    postcode VARCHAR(20),
    country_name VARCHAR(100),
    primary_category VARCHAR(255),
    secondary_category VARCHAR(255),
    company_name VARCHAR(255),
    business_description TEXT,
    keywords TEXT,
    member_chapter VARCHAR(255),
    member_chapter_id VARCHAR(100),
    role_info VARCHAR(255),
    msp_status VARCHAR(100),
    email_verified BOOLEAN DEFAULT FALSE,
    profile_data JSON,
    connections JSON,
    testimonials JSON,
    training_history JSON,
    original_search_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_uuid (uuid),
    INDEX idx_display_name (display_name),
    INDEX idx_company_name (company_name),
    INDEX idx_email (email_address)
);
"""

if __name__ == "__main__":
    check_table_schema()
