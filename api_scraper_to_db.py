import requests
import json
import mysql.connector
from mysql.connector import <PERSON>rror
import time
import logging
from datetime import datetime
import os
from typing import Dict, Any, Optional
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException
import subprocess
import platform
from dotenv import load_dotenv
import asyncio
import aiohttp
import concurrent.futures
from threading import Lock

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_scraper.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def find_chrome_driver():
    """
    Automatically find Chrome driver in the system.
    """
    possible_paths = []

    if platform.system() == "Windows":
        possible_paths = [
            "chromedriver.exe",
            "./chromedriver.exe",
            "C:/chromedriver/chromedriver.exe",
            "C:/Program Files/chromedriver/chromedriver.exe",
            "C:/Program Files (x86)/chromedriver/chromedriver.exe",
            "C:/Users/<USER>/Desktop/Web_Driver/chromedriver.exe",
            os.path.expanduser("~/Downloads/chromedriver.exe"),
            os.path.expanduser("~/Desktop/chromedriver.exe"),
        ]
    else:
        possible_paths = [
            "chromedriver",
            "./chromedriver",
            "/usr/local/bin/chromedriver",
            "/usr/bin/chromedriver",
            os.path.expanduser("~/Downloads/chromedriver"),
            os.path.expanduser("~/Desktop/chromedriver"),
        ]

    # Check if chromedriver is in PATH
    try:
        result = subprocess.run(["chromedriver", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("ChromeDriver found in PATH")
            return "chromedriver"
    except FileNotFoundError:
        pass

    # Check possible paths
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"ChromeDriver found at: {path}")
            return path

    # If not found, provide instructions
    logger.error("ChromeDriver not found!")
    logger.error("Please download ChromeDriver from: https://chromedriver.chromium.org/")
    logger.error("And place it in one of these locations:")
    for path in possible_paths[:3]:
        logger.error(f"  - {path}")
    raise FileNotFoundError("ChromeDriver not found in system")

def save_bearer_token(token):
    """Save bearer token to file."""
    with open('bearer_token.txt', 'w') as f:
        f.write(token)
    logger.info("Bearer token saved to bearer_token.txt")

def load_bearer_token():
    """Load bearer token from file."""
    try:
        with open('bearer_token.txt', 'r') as f:
            token = f.read().strip()
        if token:
            logger.info("Bearer token loaded from file")
            return token
    except FileNotFoundError:
        logger.info("No saved bearer token found")
    return None

def extract_bearer_token_from_browser():
    """
    Login to BNI Connect Global and extract bearer token from network requests.
    """
    logger.info("🔄 Starting browser to extract bearer token...")

    # Get credentials from environment
    username = os.getenv('BNI_USERNAME')
    password = os.getenv('BNI_PASSWORD')
    headless = os.getenv('HEADLESS', 'false').lower() == 'true'

    if not username or not password:
        raise ValueError("❌ BNI_USERNAME and BNI_PASSWORD must be set in .env file")

    # Find Chrome driver
    driver_path = find_chrome_driver()

    # Setup Chrome options
    chrome_options = Options()
    if headless:
        chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')

    # Enable logging for network requests
    chrome_options.add_argument('--enable-logging')
    chrome_options.add_argument('--log-level=0')
    chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})

    driver = None
    try:
        # Initialize driver
        if driver_path == "chromedriver":
            driver = webdriver.Chrome(options=chrome_options)
        else:
            service = Service(driver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)

        logger.info("🌐 Navigating to BNI Connect Global login page...")
        driver.get("https://www.bniconnectglobal.com/login")

        # Wait for login form
        wait = WebDriverWait(driver, 20)

        # Find and fill username
        username_field = wait.until(EC.presence_of_element_located((By.NAME, "username")))
        username_field.clear()
        username_field.send_keys(username)
        logger.info("✅ Username entered")

        # Find and fill password
        password_field = driver.find_element(By.NAME, "password")
        password_field.clear()
        password_field.send_keys(password)
        logger.info("✅ Password entered")

        # Click login button
        login_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        login_button.click()
        logger.info("🔄 Login button clicked")

        # Wait for successful login (dashboard page)
        wait.until(EC.url_contains("dashboard"))
        logger.info("✅ Successfully logged in")

        # Navigate to member dashboard to trigger API calls
        logger.info("🔄 Navigating to member dashboard...")
        driver.get("https://www.bniconnectglobal.com/web/dashboard/search")

        # Wait a bit for API calls to be made
        time.sleep(5)

        # Extract bearer token from network logs
        bearer_token = None
        logs = driver.get_log('performance')

        for log in logs:
            message = json.loads(log['message'])
            if message['message']['method'] == 'Network.requestWillBeSent':
                request = message['message']['params']['request']
                headers = request.get('headers', {})
                auth_header = headers.get('Authorization', '')

                if auth_header.startswith('Bearer '):
                    bearer_token = auth_header.replace('Bearer ', '')
                    logger.info("✅ Bearer token extracted from network logs")
                    break

        if not bearer_token:
            # Alternative method: try to trigger a search to generate API calls
            logger.info("🔄 Triggering search to generate API calls...")
            try:
                # Try to find search elements and trigger a search
                search_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input[placeholder*='Search']")))
                search_input.send_keys("test")
                time.sleep(2)

                # Get logs again
                logs = driver.get_log('performance')
                for log in logs:
                    message = json.loads(log['message'])
                    if message['message']['method'] == 'Network.requestWillBeSent':
                        request = message['message']['params']['request']
                        headers = request.get('headers', {})
                        auth_header = headers.get('Authorization', '')

                        if auth_header.startswith('Bearer '):
                            bearer_token = auth_header.replace('Bearer ', '')
                            logger.info("✅ Bearer token extracted from search API call")
                            break
            except Exception as e:
                logger.warning(f"⚠️ Could not trigger search: {e}")

        if bearer_token:
            save_bearer_token(bearer_token)
            return bearer_token
        else:
            raise Exception("❌ Could not extract bearer token from network logs")

    except Exception as e:
        logger.error(f"❌ Error during token extraction: {e}")
        raise
    finally:
        if driver:
            driver.quit()
            logger.info("🔄 Browser closed")

def get_current_bearer_token():
    """
    Get current bearer token, refresh if needed.
    """
    # Try to load existing token
    token = load_bearer_token()

    if not token:
        logger.info("🔄 No bearer token found, extracting new one...")
        token = extract_bearer_token_from_browser()

    return token

def make_api_request_with_retry(url, headers, max_retries=3):
    """
    Make API request with automatic bearer token refresh on failure.
    """
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=30)

            # Check if request was successful
            if response.status_code == 200:
                return response

            # Check if it's an authentication error
            elif response.status_code in [401, 403]:
                logger.warning(f"⚠️ Authentication error (attempt {attempt + 1}/{max_retries}): Status {response.status_code}")

                if attempt < max_retries - 1:  # Don't refresh token on last attempt
                    logger.info("🔄 Refreshing bearer token...")
                    new_token = extract_bearer_token_from_browser()
                    headers['Authorization'] = f'Bearer {new_token}'
                    continue
                else:
                    logger.error("❌ Max retries reached for token refresh")
                    return response

            else:
                logger.warning(f"⚠️ API request failed with status {response.status_code}")
                return response

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Request error (attempt {attempt + 1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                raise
            time.sleep(2)  # Wait before retry

    return None

class BNIAPIScraperOptimized:
    def __init__(self, db_config: Dict[str, str], max_workers: int = 10):
        """
        Initialize the optimized BNI API scraper with concurrent processing.

        Args:
            db_config: Dictionary containing database connection parameters
            max_workers: Maximum number of concurrent workers for API requests
        """
        self.db_config = db_config
        self.connection = None
        self.bearer_token = None
        self.max_workers = max_workers
        self.lock = Lock()  # For thread-safe operations

        # Session for requests
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Origin': 'https://www.bniconnectglobal.com',
            'Referer': 'https://www.bniconnectglobal.com/'
        })

        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0

        # Initialize bearer token
        self._initialize_bearer_token()

    def _initialize_bearer_token(self):
        """Initialize bearer token for API authentication."""
        try:
            logger.info("🔄 Initializing bearer token...")
            self.bearer_token = get_current_bearer_token()
            self.session.headers['Authorization'] = f'Bearer {self.bearer_token}'
            logger.info("✅ Bearer token initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize bearer token: {e}")
            raise

    def fetch_api_data_fast(self, api_url: str) -> Optional[Dict[str, Any]]:
        """
        Optimized version of fetch_api_data with reduced timeout and retries.
        """
        try:
            response = self.session.get(api_url, timeout=10)  # Reduced timeout

            if response.status_code == 200:
                try:
                    return response.json()
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON response from {api_url}")
                    return None
            elif response.status_code in [401, 403]:
                # Don't retry token refresh in fast mode - handle at batch level
                logger.warning(f"Auth error for {api_url}: {response.status_code}")
                return None
            else:
                logger.warning(f"API request failed with status {response.status_code} for {api_url}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for {api_url}: {e}")
            return None

    def process_batch_concurrent(self, endpoint_batch: list) -> list:
        """
        Process a batch of endpoints concurrently.

        Args:
            endpoint_batch: List of endpoint lines to process

        Returns:
            List of tuples (success, member_data, user_id)
        """
        results = []

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all requests in the batch
            future_to_endpoint = {}

            for line in endpoint_batch:
                api_url, uuid, user_id = self.parse_endpoint_with_userid(line)
                if api_url and uuid and user_id:
                    future = executor.submit(self.fetch_api_data_fast, api_url)
                    future_to_endpoint[future] = (api_url, uuid, user_id, line)

            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_endpoint):
                api_url, uuid, user_id, line = future_to_endpoint[future]

                try:
                    api_data = future.result()

                    if api_data is not None:
                        member_data = self.extract_member_data(api_data, uuid)
                        results.append((True, member_data, user_id))

                        with self.lock:
                            self.success_count += 1
                    else:
                        results.append((False, None, user_id))
                        with self.lock:
                            self.error_count += 1

                except Exception as e:
                    logger.error(f"Error processing {uuid}: {e}")
                    results.append((False, None, user_id))
                    with self.lock:
                        self.error_count += 1

                with self.lock:
                    self.processed_count += 1

        return results

    def insert_batch_data(self, batch_results: list) -> int:
        """
        Insert a batch of member data into database efficiently.

        Args:
            batch_results: List of (success, member_data, user_id) tuples

        Returns:
            Number of successful insertions
        """
        successful_insertions = 0

        try:
            cursor = self.connection.cursor()

            # Prepare batch insert
            insert_query = """
            INSERT INTO member_details (
                user_id, uuid, extraction_timestamp, title, display_name, first_name, last_name,
                email_address, phone_number, mobile_number, direct_number, fax_number, website_url,
                profile_image_id, address_line1, address_line2, city, state, country, postcode,
                country_name, primary_category, secondary_category, company_name, business_description,
                keywords, member_chapter, member_chapter_id, role_info, msp_status, email_verified,
                profile_data, connections, testimonials, training_history, original_search_data
            ) VALUES (
                %(user_id)s, %(uuid)s, %(extraction_timestamp)s, %(title)s, %(display_name)s, %(first_name)s, %(last_name)s,
                %(email_address)s, %(phone_number)s, %(mobile_number)s, %(direct_number)s, %(fax_number)s, %(website_url)s,
                %(profile_image_id)s, %(address_line1)s, %(address_line2)s, %(city)s, %(state)s, %(country)s, %(postcode)s,
                %(country_name)s, %(primary_category)s, %(secondary_category)s, %(company_name)s, %(business_description)s,
                %(keywords)s, %(member_chapter)s, %(member_chapter_id)s, %(role_info)s, %(msp_status)s, %(email_verified)s,
                %(profile_data)s, %(connections)s, %(testimonials)s, %(training_history)s, %(original_search_data)s
            ) ON DUPLICATE KEY UPDATE
                extraction_timestamp = VALUES(extraction_timestamp),
                title = VALUES(title),
                display_name = VALUES(display_name),
                first_name = VALUES(first_name),
                last_name = VALUES(last_name),
                email_address = VALUES(email_address),
                phone_number = VALUES(phone_number),
                mobile_number = VALUES(mobile_number),
                direct_number = VALUES(direct_number),
                fax_number = VALUES(fax_number),
                website_url = VALUES(website_url),
                profile_image_id = VALUES(profile_image_id),
                address_line1 = VALUES(address_line1),
                address_line2 = VALUES(address_line2),
                city = VALUES(city),
                state = VALUES(state),
                country = VALUES(country),
                postcode = VALUES(postcode),
                country_name = VALUES(country_name),
                primary_category = VALUES(primary_category),
                secondary_category = VALUES(secondary_category),
                company_name = VALUES(company_name),
                business_description = VALUES(business_description),
                keywords = VALUES(keywords),
                member_chapter = VALUES(member_chapter),
                member_chapter_id = VALUES(member_chapter_id),
                role_info = VALUES(role_info),
                msp_status = VALUES(msp_status),
                email_verified = VALUES(email_verified),
                profile_data = VALUES(profile_data),
                connections = VALUES(connections),
                testimonials = VALUES(testimonials),
                training_history = VALUES(training_history),
                original_search_data = VALUES(original_search_data),
                updated_at = CURRENT_TIMESTAMP
            """

            # Execute batch insert
            batch_data = []
            for success, member_data, user_id in batch_results:
                if success and member_data:
                    member_data['user_id'] = user_id
                    batch_data.append(member_data)

            if batch_data:
                cursor.executemany(insert_query, batch_data)
                self.connection.commit()
                successful_insertions = len(batch_data)

            cursor.close()

        except Error as e:
            logger.error(f"Error in batch insert: {e}")
            if self.connection:
                self.connection.rollback()

        return successful_insertions

    def connect_to_database(self) -> bool:
        """Establish connection to MySQL database."""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logger.info("Successfully connected to MySQL database")
                return True
        except Error as e:
            logger.error(f"Error connecting to MySQL database: {e}")
            return False

    def close_database_connection(self):
        """Close the database connection."""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("Database connection closed")

    def parse_endpoint_with_userid(self, line: str) -> tuple[str, str, int]:
        """Parse a line from api_endpoints_with_userid.txt to extract URL, UUID, and user_id."""
        try:
            parts = line.strip().split()
            if len(parts) != 2:
                raise ValueError(f"Invalid line format: {line}")

            api_url = parts[0]
            user_id_part = parts[1]
            uuid = api_url.split('uuId=')[-1].strip()

            if not user_id_part.startswith('user_id='):
                raise ValueError(f"Invalid user_id format: {user_id_part}")

            user_id = int(user_id_part.split('=')[1])
            return api_url, uuid, user_id

        except Exception as e:
            logger.error(f"Error parsing line '{line}': {e}")
            return None, None, None

    def extract_member_data(self, api_data: Dict[str, Any], uuid: str) -> Dict[str, Any]:
        """Extract and map member data from API response to database fields."""
        # Initialize with default values
        member_data = {
            'uuid': uuid,
            'extraction_timestamp': datetime.now(),
            'title': None,
            'display_name': None,
            'first_name': None,
            'last_name': None,
            'email_address': None,
            'phone_number': None,
            'mobile_number': None,
            'direct_number': None,
            'fax_number': None,
            'website_url': None,
            'profile_image_id': None,
            'address_line1': None,
            'address_line2': None,
            'city': None,
            'state': None,
            'country': None,
            'postcode': None,
            'country_name': None,
            'primary_category': None,
            'secondary_category': None,
            'company_name': None,
            'business_description': None,
            'keywords': None,
            'member_chapter': None,
            'member_chapter_id': None,
            'role_info': None,
            'msp_status': None,
            'email_verified': False,
            'profile_data': json.dumps(api_data) if api_data else None,
            'connections': None,
            'testimonials': None,
            'training_history': None,
            'original_search_data': json.dumps(api_data) if api_data else None
        }

        # Extract data from API response (data is nested in 'content')
        if api_data and isinstance(api_data, dict):
            content = api_data.get('content', {})
            if isinstance(content, dict):
                # Basic member information
                member_data['display_name'] = content.get('displayName') or content.get('name')
                member_data['first_name'] = content.get('firstName')
                member_data['last_name'] = content.get('lastName')
                member_data['title'] = content.get('title')
                member_data['email_address'] = content.get('emailAddress') or content.get('email')

                # Contact information
                member_data['phone_number'] = content.get('phoneNumber') or content.get('phone')
                member_data['mobile_number'] = content.get('mobileNumber') or content.get('mobile')
                member_data['direct_number'] = content.get('directNumber')
                member_data['fax_number'] = content.get('faxNumber')
                member_data['website_url'] = content.get('websiteUrl') or content.get('website')

                # Address information
                address = content.get('address', {})
                if isinstance(address, dict):
                    member_data['address_line1'] = address.get('line1') or address.get('addressLine1')
                    member_data['address_line2'] = address.get('line2') or address.get('addressLine2')
                    member_data['city'] = address.get('city')
                    member_data['state'] = address.get('state') or address.get('stateProvince')
                    member_data['country'] = address.get('country') or address.get('countryCode')
                    member_data['postcode'] = address.get('postcode') or address.get('zipCode')
                    member_data['country_name'] = address.get('countryName')

                # Business information
                member_data['company_name'] = content.get('companyName') or content.get('company')
                member_data['business_description'] = content.get('businessDescription') or content.get('description')
                member_data['primary_category'] = content.get('primaryCategory') or content.get('category')
                member_data['secondary_category'] = content.get('secondaryCategory')
                member_data['keywords'] = content.get('keywords')

                # Chapter and role information
                chapter = content.get('chapter', {})
                if isinstance(chapter, dict):
                    member_data['member_chapter'] = chapter.get('name') or chapter.get('chapterName')
                    member_data['member_chapter_id'] = chapter.get('id') or chapter.get('chapterId')

                member_data['role_info'] = content.get('roleInfo') or content.get('role') or content.get('memberRole')
                member_data['msp_status'] = content.get('mspStatus')
                member_data['email_verified'] = bool(content.get('emailVerified', False))

                # Profile image
                member_data['profile_image_id'] = content.get('profileImageId') or content.get('imageId')

                # Additional data as JSON
                if content.get('connections'):
                    member_data['connections'] = json.dumps(content['connections'])
                if content.get('testimonials'):
                    member_data['testimonials'] = json.dumps(content['testimonials'])
                if content.get('trainingHistory'):
                    member_data['training_history'] = json.dumps(content['trainingHistory'])

        return member_data

    def process_all_endpoints_optimized(self, endpoints_file: str, batch_size: int = 50, delay: float = 0.1):
        """
        Optimized processing of all API endpoints with concurrent requests.

        Args:
            endpoints_file: Path to file containing API endpoints
            batch_size: Number of requests to process concurrently in each batch
            delay: Minimal delay between batches (not individual requests)
        """
        try:
            with open(endpoints_file, 'r', encoding='utf-8') as f:
                endpoint_lines = [line.strip() for line in f if line.strip()]

            total_endpoints = len(endpoint_lines)
            logger.info(f"Starting optimized processing of {total_endpoints} API endpoints")
            logger.info(f"Batch size: {batch_size}, Max workers: {self.max_workers}")

            start_time = time.time()

            # Process in batches
            for i in range(0, total_endpoints, batch_size):
                batch = endpoint_lines[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                total_batches = (total_endpoints + batch_size - 1) // batch_size

                logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} endpoints)")

                # Process batch concurrently
                batch_results = self.process_batch_concurrent(batch)

                # Insert batch results to database
                successful_insertions = self.insert_batch_data(batch_results)

                # Progress reporting
                elapsed_time = time.time() - start_time
                processed_so_far = min(i + batch_size, total_endpoints)
                avg_time_per_request = elapsed_time / processed_so_far
                estimated_remaining = (total_endpoints - processed_so_far) * avg_time_per_request

                logger.info(f"Batch {batch_num} completed: {successful_insertions}/{len(batch)} successful")
                logger.info(f"Overall progress: {processed_so_far}/{total_endpoints} ({processed_so_far/total_endpoints*100:.1f}%)")
                logger.info(f"Success: {self.success_count}, Errors: {self.error_count}")
                logger.info(f"Avg time per request: {avg_time_per_request:.3f}s")
                logger.info(f"Estimated time remaining: {estimated_remaining/60:.1f} minutes")

                # Small delay between batches to be respectful
                if delay > 0 and i + batch_size < total_endpoints:
                    time.sleep(delay)

            # Final statistics
            total_time = time.time() - start_time
            logger.info(f"Optimized processing completed!")
            logger.info(f"Total processed: {self.processed_count}")
            logger.info(f"Successful: {self.success_count}")
            logger.info(f"Errors: {self.error_count}")
            logger.info(f"Total time: {total_time/60:.1f} minutes")
            logger.info(f"Average time per request: {total_time/total_endpoints:.3f} seconds")
            logger.info(f"Speed improvement: ~{2.0/(total_time/total_endpoints):.1f}x faster than original")

        except FileNotFoundError:
            logger.error(f"Endpoints file not found: {endpoints_file}")
        except Exception as e:
            logger.error(f"Error in optimized processing: {e}")


class BNIAPIScraper:
    def __init__(self, db_config: Dict[str, str]):
        """
        Initialize the BNI API scraper with database configuration.

        Args:
            db_config: Dictionary containing database connection parameters
        """
        self.db_config = db_config
        self.connection = None
        self.session = requests.Session()
        self.bearer_token = None

        # Set up session headers to mimic a real browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Origin': 'https://www.bniconnectglobal.com',
            'Referer': 'https://www.bniconnectglobal.com/'
        })

        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0

        # Initialize bearer token
        self._initialize_bearer_token()

    def _initialize_bearer_token(self):
        """Initialize bearer token for API authentication."""
        try:
            logger.info("🔄 Initializing bearer token...")
            self.bearer_token = get_current_bearer_token()
            self.session.headers['Authorization'] = f'Bearer {self.bearer_token}'
            logger.info("✅ Bearer token initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize bearer token: {e}")
            raise
        
    def connect_to_database(self) -> bool:
        """
        Establish connection to MySQL database.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logger.info("Successfully connected to MySQL database")
                return True
        except Error as e:
            logger.error(f"Error connecting to MySQL database: {e}")
            return False
        
    def close_database_connection(self):
        """Close the database connection."""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("Database connection closed")
    
    def fetch_api_data(self, api_url: str) -> Optional[Dict[str, Any]]:
        """
        Fetch data from a single API endpoint with bearer token authentication and retry logic.

        Args:
            api_url: The API URL to fetch data from

        Returns:
            Dict containing the API response data or None if failed
        """
        max_retries = 3

        for attempt in range(max_retries):
            try:
                response = self.session.get(api_url, timeout=30)

                # Check if request was successful
                if response.status_code == 200:
                    # Try to parse JSON response
                    try:
                        data = response.json()
                        return data
                    except json.JSONDecodeError:
                        logger.warning(f"Invalid JSON response from {api_url}")
                        return None

                # Check if it's an authentication error
                elif response.status_code in [401, 403]:
                    logger.warning(f"⚠️ Authentication error (attempt {attempt + 1}/{max_retries}): Status {response.status_code}")

                    if attempt < max_retries - 1:  # Don't refresh token on last attempt
                        logger.info("🔄 Refreshing bearer token...")
                        self.bearer_token = extract_bearer_token_from_browser()
                        self.session.headers['Authorization'] = f'Bearer {self.bearer_token}'
                        continue
                    else:
                        logger.error("❌ Max retries reached for token refresh")
                        return None

                else:
                    logger.warning(f"⚠️ API request failed with status {response.status_code}")
                    return None

            except requests.exceptions.RequestException as e:
                logger.error(f"❌ Request error (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return None
                time.sleep(2)  # Wait before retry

        return None
    
    def extract_member_data(self, api_data: Dict[str, Any], uuid: str) -> Dict[str, Any]:
        """
        Extract and map member data from API response to database fields.
        
        Args:
            api_data: Raw API response data
            uuid: The UUID for this member
            
        Returns:
            Dict containing mapped member data
        """
        # Initialize with default values
        member_data = {
            'uuid': uuid,
            'extraction_timestamp': datetime.now(),
            'title': None,
            'display_name': None,
            'first_name': None,
            'last_name': None,
            'email_address': None,
            'phone_number': None,
            'mobile_number': None,
            'direct_number': None,
            'fax_number': None,
            'website_url': None,
            'profile_image_id': None,
            'address_line1': None,
            'address_line2': None,
            'city': None,
            'state': None,
            'country': None,
            'postcode': None,
            'country_name': None,
            'primary_category': None,
            'secondary_category': None,
            'company_name': None,
            'business_description': None,
            'keywords': None,
            'member_chapter': None,
            'member_chapter_id': None,
            'role_info': None,
            'msp_status': None,
            'email_verified': False,
            'profile_data': json.dumps(api_data) if api_data else None,
            'connections': None,
            'testimonials': None,
            'training_history': None,
            'original_search_data': json.dumps(api_data) if api_data else None
        }
        
        # Extract data from API response (data is nested in 'content')
        if api_data and isinstance(api_data, dict):
            content = api_data.get('content', {})
            if isinstance(content, dict):
                # Basic member information
                member_data['display_name'] = content.get('displayName') or content.get('name')
                member_data['first_name'] = content.get('firstName')
                member_data['last_name'] = content.get('lastName')
                member_data['title'] = content.get('title')
                member_data['email_address'] = content.get('emailAddress') or content.get('email')

                # Contact information
                member_data['phone_number'] = content.get('phoneNumber') or content.get('phone')
                member_data['mobile_number'] = content.get('mobileNumber') or content.get('mobile')
                member_data['direct_number'] = content.get('directNumber')
                member_data['fax_number'] = content.get('faxNumber')
                member_data['website_url'] = content.get('websiteUrl') or content.get('website')
            
                # Address information
                address = content.get('address', {})
                if isinstance(address, dict):
                    member_data['address_line1'] = address.get('line1') or address.get('addressLine1')
                    member_data['address_line2'] = address.get('line2') or address.get('addressLine2')
                    member_data['city'] = address.get('city')
                    member_data['state'] = address.get('state') or address.get('stateProvince')
                    member_data['country'] = address.get('country') or address.get('countryCode')
                    member_data['postcode'] = address.get('postcode') or address.get('zipCode')
                    member_data['country_name'] = address.get('countryName')

                # Business information
                member_data['company_name'] = content.get('companyName') or content.get('company')
                member_data['business_description'] = content.get('businessDescription') or content.get('description')
                member_data['primary_category'] = content.get('primaryCategory') or content.get('category')
                member_data['secondary_category'] = content.get('secondaryCategory')
                member_data['keywords'] = content.get('keywords')

                # Chapter and role information
                chapter = content.get('chapter', {})
                if isinstance(chapter, dict):
                    member_data['member_chapter'] = chapter.get('name') or chapter.get('chapterName')
                    member_data['member_chapter_id'] = chapter.get('id') or chapter.get('chapterId')

                member_data['role_info'] = content.get('roleInfo') or content.get('role') or content.get('memberRole')
                member_data['msp_status'] = content.get('mspStatus')
                member_data['email_verified'] = bool(content.get('emailVerified', False))

                # Profile image
                member_data['profile_image_id'] = content.get('profileImageId') or content.get('imageId')

                # Additional data as JSON
                if content.get('connections'):
                    member_data['connections'] = json.dumps(content['connections'])
                if content.get('testimonials'):
                    member_data['testimonials'] = json.dumps(content['testimonials'])
                if content.get('trainingHistory'):
                    member_data['training_history'] = json.dumps(content['trainingHistory'])
        
        return member_data
    
    def parse_endpoint_with_userid(self, line: str) -> tuple[str, str, int]:
        """
        Parse a line from api_endpoints_with_userid.txt to extract URL, UUID, and user_id.

        Args:
            line: Line in format "URL user_id=12345"

        Returns:
            tuple: (api_url, uuid, user_id)
        """
        try:
            # Split by whitespace to separate URL and user_id part
            parts = line.strip().split()
            if len(parts) != 2:
                raise ValueError(f"Invalid line format: {line}")

            api_url = parts[0]
            user_id_part = parts[1]

            # Extract UUID from URL
            uuid = api_url.split('uuId=')[-1].strip()

            # Extract user_id from "user_id=12345"
            if not user_id_part.startswith('user_id='):
                raise ValueError(f"Invalid user_id format: {user_id_part}")

            user_id = int(user_id_part.split('=')[1])

            return api_url, uuid, user_id

        except Exception as e:
            logger.error(f"Error parsing line '{line}': {e}")
            return None, None, None
    
    def insert_member_data(self, member_data: Dict[str, Any], user_id: int) -> bool:
        """
        Insert member data into the database.

        Args:
            member_data: Dictionary containing member data
            user_id: The user_id for this member

        Returns:
            bool: True if insertion successful, False otherwise
        """
        try:
            # Use the provided user_id
            member_data['user_id'] = user_id
            
            cursor = self.connection.cursor()
            
            # Prepare INSERT statement with ON DUPLICATE KEY UPDATE
            insert_query = """
            INSERT INTO member_details (
                user_id, uuid, extraction_timestamp, title, display_name, first_name, last_name,
                email_address, phone_number, mobile_number, direct_number, fax_number, website_url,
                profile_image_id, address_line1, address_line2, city, state, country, postcode,
                country_name, primary_category, secondary_category, company_name, business_description,
                keywords, member_chapter, member_chapter_id, role_info, msp_status, email_verified,
                profile_data, connections, testimonials, training_history, original_search_data
            ) VALUES (
                %(user_id)s, %(uuid)s, %(extraction_timestamp)s, %(title)s, %(display_name)s, %(first_name)s, %(last_name)s,
                %(email_address)s, %(phone_number)s, %(mobile_number)s, %(direct_number)s, %(fax_number)s, %(website_url)s,
                %(profile_image_id)s, %(address_line1)s, %(address_line2)s, %(city)s, %(state)s, %(country)s, %(postcode)s,
                %(country_name)s, %(primary_category)s, %(secondary_category)s, %(company_name)s, %(business_description)s,
                %(keywords)s, %(member_chapter)s, %(member_chapter_id)s, %(role_info)s, %(msp_status)s, %(email_verified)s,
                %(profile_data)s, %(connections)s, %(testimonials)s, %(training_history)s, %(original_search_data)s
            ) ON DUPLICATE KEY UPDATE
                extraction_timestamp = VALUES(extraction_timestamp),
                title = VALUES(title),
                display_name = VALUES(display_name),
                first_name = VALUES(first_name),
                last_name = VALUES(last_name),
                email_address = VALUES(email_address),
                phone_number = VALUES(phone_number),
                mobile_number = VALUES(mobile_number),
                direct_number = VALUES(direct_number),
                fax_number = VALUES(fax_number),
                website_url = VALUES(website_url),
                profile_image_id = VALUES(profile_image_id),
                address_line1 = VALUES(address_line1),
                address_line2 = VALUES(address_line2),
                city = VALUES(city),
                state = VALUES(state),
                country = VALUES(country),
                postcode = VALUES(postcode),
                country_name = VALUES(country_name),
                primary_category = VALUES(primary_category),
                secondary_category = VALUES(secondary_category),
                company_name = VALUES(company_name),
                business_description = VALUES(business_description),
                keywords = VALUES(keywords),
                member_chapter = VALUES(member_chapter),
                member_chapter_id = VALUES(member_chapter_id),
                role_info = VALUES(role_info),
                msp_status = VALUES(msp_status),
                email_verified = VALUES(email_verified),
                profile_data = VALUES(profile_data),
                connections = VALUES(connections),
                testimonials = VALUES(testimonials),
                training_history = VALUES(training_history),
                original_search_data = VALUES(original_search_data),
                updated_at = CURRENT_TIMESTAMP
            """
            
            cursor.execute(insert_query, member_data)
            self.connection.commit()
            cursor.close()
            
            return True
            
        except Error as e:
            logger.error(f"Error inserting member data for UUID {member_data.get('uuid')}: {e}")
            if self.connection:
                self.connection.rollback()
            return False

    def process_single_endpoint(self, line: str) -> bool:
        """
        Process a single endpoint line: parse, fetch data and store in database.

        Args:
            line: Line from file in format "URL user_id=12345"

        Returns:
            bool: True if processing successful, False otherwise
        """
        try:
            # Parse the line to extract URL, UUID, and user_id
            api_url, uuid, user_id = self.parse_endpoint_with_userid(line)

            if not api_url or not uuid or not user_id:
                logger.error(f"Failed to parse line: {line}")
                return False

            logger.info(f"Processing UUID: {uuid} with user_id: {user_id}")

            # Fetch data from API
            api_data = self.fetch_api_data(api_url)

            if api_data is None:
                logger.warning(f"No data received for UUID: {uuid}")
                return False

            # Log the first few API responses to understand the structure
            if self.processed_count <= 3:
                logger.info(f"Sample API response for UUID {uuid}: {json.dumps(api_data, indent=2)[:500]}...")

            # Extract and map member data
            member_data = self.extract_member_data(api_data, uuid)

            # Insert into database with the provided user_id
            success = self.insert_member_data(member_data, user_id)

            if success:
                logger.info(f"Successfully processed UUID: {uuid} with user_id: {user_id}")
                return True
            else:
                logger.error(f"Failed to insert data for UUID: {uuid}")
                return False

        except Exception as e:
            logger.error(f"Unexpected error processing line '{line}': {e}")
            return False

    def process_all_endpoints(self, endpoints_file: str, batch_size: int = 100, delay: float = 1.0):
        """
        Process all API endpoints from the file.

        Args:
            endpoints_file: Path to file containing API endpoints
            batch_size: Number of requests to process before committing to database
            delay: Delay between requests in seconds
        """
        try:
            with open(endpoints_file, 'r', encoding='utf-8') as f:
                endpoint_lines = [line.strip() for line in f if line.strip()]

            total_endpoints = len(endpoint_lines)
            logger.info(f"Starting to process {total_endpoints} API endpoints with user_ids")

            start_time = time.time()

            for i, endpoint_line in enumerate(endpoint_lines, 1):
                self.processed_count += 1

                # Process the endpoint line (contains URL and user_id)
                success = self.process_single_endpoint(endpoint_line)

                if success:
                    self.success_count += 1
                else:
                    self.error_count += 1

                # Progress reporting
                if i % batch_size == 0:
                    elapsed_time = time.time() - start_time
                    avg_time_per_request = elapsed_time / i
                    estimated_remaining = (total_endpoints - i) * avg_time_per_request

                    logger.info(f"Progress: {i}/{total_endpoints} ({i/total_endpoints*100:.1f}%)")
                    logger.info(f"Success: {self.success_count}, Errors: {self.error_count}")
                    logger.info(f"Estimated time remaining: {estimated_remaining/60:.1f} minutes")

                # Add delay between requests to be respectful to the API
                if delay > 0:
                    time.sleep(delay)

            # Final statistics
            total_time = time.time() - start_time
            logger.info(f"Processing completed!")
            logger.info(f"Total processed: {self.processed_count}")
            logger.info(f"Successful: {self.success_count}")
            logger.info(f"Errors: {self.error_count}")
            logger.info(f"Total time: {total_time/60:.1f} minutes")
            logger.info(f"Average time per request: {total_time/total_endpoints:.2f} seconds")

        except FileNotFoundError:
            logger.error(f"Endpoints file not found: {endpoints_file}")
        except Exception as e:
            logger.error(f"Error processing endpoints: {e}")


def main():
    """Main function to run the API scraper with optimization options."""

    # Database configuration - UPDATE THESE VALUES
    db_config = {
        'host': 'localhost',  # Change to your MySQL host
        'port': 3306,         # Change to your MySQL port
        'database': 'bni',    # Change to your database name
        'user': 'root',       # Change to your MySQL username
        'password': '',       # Change to your MySQL password
        'charset': 'utf8mb4',
        'use_unicode': True,
        'autocommit': False
    }

    # Configuration
    endpoints_file = 'api_endpoints_with_userid.txt'

    # Ask user for processing mode
    print("\n🚀 BNI API Scraper - Performance Options:")
    print("1. FAST MODE (Optimized) - ~10x faster with concurrent processing")
    print("2. SAFE MODE (Original) - Slower but more conservative")

    while True:
        choice = input("\nSelect mode (1 for FAST, 2 for SAFE): ").strip()
        if choice in ['1', '2']:
            break
        print("Please enter 1 or 2")

    use_optimized = choice == '1'

    if use_optimized:
        # OPTIMIZED SETTINGS
        max_workers = 10  # Concurrent requests
        batch_size = 50   # Process in batches
        delay_between_batches = 0.1  # Minimal delay between batches

        logger.info("🚀 Using OPTIMIZED mode with concurrent processing")
        logger.info(f"Max concurrent workers: {max_workers}")
        logger.info(f"Batch size: {batch_size}")
        logger.info(f"Delay between batches: {delay_between_batches}s")

        # Create optimized scraper instance
        scraper = BNIAPIScraperOptimized(db_config, max_workers=max_workers)

        try:
            # Connect to database
            if not scraper.connect_to_database():
                logger.error("Failed to connect to database. Exiting.")
                return

            # Check if endpoints file exists
            if not os.path.exists(endpoints_file):
                logger.error(f"Endpoints file '{endpoints_file}' not found.")
                return

            logger.info("Starting OPTIMIZED BNI API scraping process...")

            # Process all endpoints with optimization
            scraper.process_all_endpoints_optimized(endpoints_file, batch_size, delay_between_batches)

        except KeyboardInterrupt:
            logger.info("Process interrupted by user")
        except Exception as e:
            logger.error(f"Unexpected error in optimized main: {e}")
        finally:
            scraper.close_database_connection()
            logger.info("Optimized script execution completed")

    else:
        # ORIGINAL SETTINGS (but with reduced delay)
        batch_size = 50
        delay_between_requests = 0.5  # Reduced from 2.0 to 0.5 seconds

        logger.info("🐌 Using SAFE mode (original method with reduced delay)")
        logger.info(f"Batch size: {batch_size}")
        logger.info(f"Delay between requests: {delay_between_requests}s")

        # Create original scraper instance
        scraper = BNIAPIScraper(db_config)

        try:
            # Connect to database
            if not scraper.connect_to_database():
                logger.error("Failed to connect to database. Exiting.")
                return

            # Check if endpoints file exists
            if not os.path.exists(endpoints_file):
                logger.error(f"Endpoints file '{endpoints_file}' not found.")
                return

            logger.info("Starting SAFE BNI API scraping process...")

            # Process all endpoints
            scraper.process_all_endpoints(endpoints_file, batch_size, delay_between_requests)

        except KeyboardInterrupt:
            logger.info("Process interrupted by user")
        except Exception as e:
            logger.error(f"Unexpected error in safe main: {e}")
        finally:
            scraper.close_database_connection()
            logger.info("Safe script execution completed")


if __name__ == "__main__":
    main()
